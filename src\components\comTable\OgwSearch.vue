<template>
  <div class="search-bar">
    <el-form :inline="true" :model="formData" class="form-body">
      <el-form-item
        v-for="(field, index) in visibleFields"
        :key="field.prop"
        :label="field.label"
        :class="[
          { 'month-range': field.type === 'monthrange' },
          { 'date-range': field.type === 'daterange' },
          { month: field.type === 'month' },
          { year: field.type === 'year' },
        ]"
      >
        <!-- Select -->
        <el-select
          :popper-append-to-body="false"
          v-if="field.type === 'select'"
          v-model="formData[field.prop]"
          :placeholder="field.placeholder || '请选择'"
          clearable
          :style="getSelectStyle(field.prop)"
          @visible-change="handleSelectVisibleChange"
        >
          <el-option
            v-for="opt in (field.options || []).filter(Boolean)"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>

        <!-- Input with debounce -->
        <el-input
          v-else-if="field.type === 'input'"
          v-model="inputBuffer[field.prop]"
          :placeholder="field.placeholder || '请输入'"
          clearable
          @input="debounceInput(field.prop)"
        />

        <!-- Date:year -->
        <el-date-picker
          :append-to-body="false"
          v-else-if="field.type === 'year'"
          v-model="formData[field.prop]"
          type="year"
          :placeholder="field.placeholder || '选择年份'"
          format="yyyy年"
          value-format="yyyy"
          clearable
        />

        <!-- Date: month -->
        <el-date-picker
          :append-to-body="false"
          v-else-if="field.type === 'month'"
          v-model="formData[field.prop]"
          type="month"
          :placeholder="field.placeholder || '选择月份'"
          format="yyyy年MM月"
          value-format="yyyy-MM"
          clearable
        />

        <!-- Date range -->
        <el-date-picker
          :append-to-body="false"
          v-else-if="field.type === 'daterange'"
          v-model="formData[field.prop]"
          type="daterange"
          :start-placeholder="field.startPlaceholder || '开始日期'"
          :end-placeholder="field.endPlaceholder || '结束日期'"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          clearable
        />

        <div v-else-if="field.type === 'monthrange'">
          <el-date-picker
            :append-to-body="false"
            v-model="formData[field.prop][0]"
            type="month"
            :placeholder="field.startPlaceholder || '开始月份'"
            format="yyyy年MM月"
            value-format="yyyy-MM"
            clearable
            style="width: 140px"
          />
          <span style="margin: 0 5px">至</span>
          <el-date-picker
            :append-to-body="false"
            v-model="formData[field.prop][1]"
            type="month"
            :placeholder="field.endPlaceholder || '结束月份'"
            format="yyyy年MM月"
            value-format="yyyy-MM"
            clearable
            style="width: 140px"
          />
        </div>
      </el-form-item>

      <!-- 按钮 -->
      <el-form-item class="btns">
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
        <slot name="actions" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "SearchBar",
  props: {
    fields: {
      type: Array,
      required: true,
    },
    value: {
      type: Object,
      default: () => ({}),
    },
    baseVisibleCount: {
      type: Number,
      default: 3, // 默认展示前 N 项
    },
    debounceTime: {
      type: Number,
      default: 400, // ms
    },
  },
  data() {
    return {
      formData: {},
      inputBuffer: {},
      debounceTimers: {},
      selectWidths: {}, // 存储每个select的动态宽度
    };
  },
  created() {
    // 在created钩子中初始化formData，确保props已经可用
    this.initializeFormData();
  },
  computed: {
    visibleFields() {
      return this.fields.filter((f) => f.visible !== false);
    },
  },
  watch: {
    value: {
      handler(newVal, oldVal) {
        // 避免不必要的更新
        if (!newVal || JSON.stringify(newVal) === JSON.stringify(oldVal)) {
          return;
        }

        // 重新初始化表单数据
        this.initializeFormData();
      },
      immediate: true,
    },

    // 监听fields变化，重新初始化
    fields: {
      handler() {
        if (this.fields && Array.isArray(this.fields)) {
          this.initializeFormData();
        }
      },
      immediate: true,
    },
    formData: {
      handler(val, oldVal) {
        // 避免循环触发
        if (JSON.stringify(val) !== JSON.stringify(oldVal)) {
          this.$emit("input", { ...val });
        }
      },
    },
  },
  methods: {
    // 初始化表单数据
    initializeFormData() {
      // 安全地获取初始值，避免响应式对象问题
      const initialValue = this.value ? JSON.parse(JSON.stringify(this.value)) : {};
      const formData = { ...initialValue };

      // 确保所有字段都有初始值
      if (this.fields && Array.isArray(this.fields)) {
        this.fields.forEach((field) => {
          if (!(field.prop in formData)) {
            formData[field.prop] =
              field.type === "daterange" || field.type === "monthrange" ? [] : "";
          }
        });
      }

      // 更新数据
      this.formData = formData;
      this.inputBuffer = { ...formData };
    },

    handleSearch() {
      this.$emit("search", { ...this.formData });
    },
    handleReset() {
      const cleared = {};
      this.visibleFields.forEach(
        (f) =>
          (cleared[f.prop] =
            f.type === "daterange" || f.type === "monthrange" ? [] : "")
      );
      this.formData = cleared;
      this.inputBuffer = { ...cleared };
      this.$emit("reset", { ...this.formData });
    },
    debounceInput(prop) {
      if (this.debounceTimers[prop]) clearTimeout(this.debounceTimers[prop]);
      this.debounceTimers[prop] = setTimeout(() => {
        this.formData[prop] = this.inputBuffer[prop];
      }, this.debounceTime);
    },
    // 计算select组件的动态宽度
    getSelectStyle(prop) {
      const selectedValue = this.formData[prop];
      if (!selectedValue) {
        return { minWidth: "120px" };
      }

      // 查找对应的选项
      const field = this.visibleFields.find((f) => f.prop === prop);
      const selectedOption = field?.options?.find(
        (opt) => opt.value === selectedValue
      );

      if (selectedOption) {
        // 根据文本长度计算宽度，每个字符大约14px，加上padding和图标空间
        const textLength = selectedOption.label.length;
        const calculatedWidth = Math.max(120, textLength * 14 + 60);
        return {
          minWidth: "120px",
          width: `${Math.min(calculatedWidth, 300)}px`, // 最大宽度限制为300px
        };
      }

      return { minWidth: "120px" };
    },
    // 处理select下拉框显示/隐藏事件
    handleSelectVisibleChange(visible) {
      // 当下拉框关闭时，重新计算宽度
      if (!visible) {
        this.$nextTick(() => {
          this.$forceUpdate();
        });
      }
    },
  },
  beforeDestroy() {
    // 清理定时器，防止内存泄漏
    Object.values(this.debounceTimers).forEach((timer) => {
      if (timer) clearTimeout(timer);
    });
  },
};
</script>

<style lang="scss" scoped>
.search-bar {
  margin: 20px 0;
  padding-right: 0;

  // 优化表单布局
  .form-body {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 16px; // 统一间距
  }
}

.btns {
  min-width: 200px !important;
  flex-shrink: 0; // 防止按钮被压缩
}

// 动态宽度的表单项
::v-deep .el-form-item {
  margin-right: 0 !important;
  margin-bottom: 16px !important;
  flex-shrink: 0; // 防止被压缩

  // 默认最小宽度
  &:not(.month-range):not(.date-range):not(.month):not(.year) {
    min-width: 120px;
  }

  .el-form-item__content {
    min-width: 120px;
  }
}

// 特殊类型的宽度设置
::v-deep .month-range {
  width: 320px !important;
  flex-shrink: 0;
  .el-form-item__content {
    width: 100% !important;
  }
}

::v-deep .date-range {
  width: 280px !important;
  flex-shrink: 0;
  .el-form-item__content {
    width: 100% !important;
  }
}

::v-deep .year {
  width: 160px !important;
  flex-shrink: 0;
  .el-date-editor.el-input {
    width: 100% !important;
  }
}

::v-deep .month {
  width: 180px !important;
  flex-shrink: 0;
  .el-date-editor.el-input {
    width: 136px;
  }
}

// 浅色主题样式
[data-theme="default"],
[data-theme="tint"] {
  .search-bar {
    // 输入框样式
    ::v-deep .el-input__inner {
      background-color: #fff !important;
      border-color: #e2e2e2 !important;
      color: #000 !important;
    }

    // 表单标签颜色
    ::v-deep .el-form-item__label {
      color: #606266 !important;
    }

    // 下拉选择器样式
    ::v-deep .el-select-dropdown {
      background-color: #fff !important;
      border-color: #e2e2e2 !important;
    }

    ::v-deep .el-select-dropdown__item {
      color: #606266 !important;

      &:hover {
        background-color: #f5f7fa !important;
      }

      &.selected {
        background-color: #409eff !important;
        color: #fff !important;
      }

      &.is-disabled {
        color: #c0c4cc !important;
      }
    }

    // 下拉箭头
    ::v-deep .el-select .el-input .el-select__caret {
      color: #c0c4cc !important;

      &:hover {
        color: #909399 !important;
      }
    }

    // 选择器标签
    ::v-deep .el-select .el-select__tags {
      .el-tag {
        background-color: #f0f2f5 !important;
        border-color: #d9d9d9 !important;
        color: #606266 !important;

        .el-tag__close {
          color: #909399 !important;

          &:hover {
            background-color: #c0c4cc !important;
            color: #fff !important;
          }
        }
      }
    }

    // 日期选择器样式
    ::v-deep .el-picker-panel {
      background-color: #fff !important;
      border-color: #e2e2e2 !important;
    }

    ::v-deep .el-date-table th {
      color: #606266 !important;
    }

    ::v-deep .el-date-table td {
      color: #606266 !important;

      &.available:hover {
        background-color: #f2f6fc !important;
      }

      &.current:not(.disabled) {
        background-color: #409eff !important;
        color: #fff !important;
      }
    }

    ::v-deep .el-picker-panel__icon-btn {
      color: #606266 !important;

      &:hover {
        color: #409eff !important;
      }
    }

    ::v-deep .el-picker-panel__shortcut {
      color: #606266 !important;

      &:hover {
        color: #409eff !important;
      }
    }

    ::v-deep .el-year-table td {
      color: #606266 !important;

      &.available:hover {
        background-color: #f2f6fc !important;
      }

      &.current:not(.disabled) {
        background-color: #409eff !important;
        color: #fff !important;
      }
    }

    ::v-deep .el-month-table td {
      color: #606266 !important;

      &.available:hover {
        background-color: #f2f6fc !important;
      }

      &.current:not(.disabled) {
        background-color: #409eff !important;
        color: #fff !important;
      }
    }

    // 按钮样式
    ::v-deep .el-button {
      &:not(.el-button--primary) {
        background-color: #fff !important;
        border-color: #dcdfe6 !important;
        color: #606266 !important;

        &:hover {
          background-color: #ecf5ff !important;
          border-color: #b3d8ff !important;
          color: #409eff !important;
        }
      }
    }

    // 占位符颜色
    ::v-deep .el-input__inner::placeholder {
      color: #c0c4cc !important;
    }

    // 清除按钮
    ::v-deep .el-input__suffix {
      .el-input__icon {
        color: #c0c4cc !important;

        &:hover {
          color: #909399 !important;
        }
      }
    }

    // 月份范围选择器中间文字
    .month-range span {
      color: #606266;
    }
  }
}

// 深色主题样式
[data-theme="dark"] {
  .search-bar {
    // 输入框样式
    ::v-deep .el-input__inner {
      background-color: #1a2e52 !important;
      border-color: #4f98f6 !important;
      color: #ffffff !important;
    }

    // 表单标签颜色
    ::v-deep .el-form-item__label {
      color: #cce4ff !important;
    }

    // 下拉选择器样式
    ::v-deep .el-select-dropdown {
      background-color: #1a2e52 !important;
      border-color: #4f98f6 !important;
    }

    ::v-deep .el-select-dropdown__item {
      color: #cce4ff !important;

      &:hover {
        background-color: #254489 !important;
      }

      &.selected {
        background-color: #4ea0fc !important;
        color: #ffffff !important;
      }

      &.is-disabled {
        color: #6493d4 !important;
      }
    }

    // 下拉箭头
    ::v-deep .el-select .el-input .el-select__caret {
      color: #6493d4 !important;

      &:hover {
        color: #cce4ff !important;
      }
    }

    // 选择器标签
    ::v-deep .el-select .el-select__tags {
      .el-tag {
        background-color: #254489 !important;
        border-color: #4f98f6 !important;
        color: #cce4ff !important;

        .el-tag__close {
          color: #cce4ff !important;

          &:hover {
            background-color: #4ea0fc !important;
            color: #ffffff !important;
          }
        }
      }
    }

    // 日期选择器样式
    ::v-deep .el-picker-panel {
      background-color: #1a2e52 !important;
      border-color: #4f98f6 !important;
    }

    ::v-deep .el-date-table th {
      color: #cce4ff !important;
    }

    ::v-deep .el-date-table td {
      color: #cce4ff !important;

      // 可用日期悬停状态
      &.available:hover {
        background-color: #254489 !important;
        color: #ffffff !important;
      }

      // 当前选中日期 - 确保高对比度
      &.current:not(.disabled) {
        background-color: #4ea0fc !important;
        color: #ffffff !important;
        font-weight: bold !important;
        &.cell {
          color: #ffffff !important;
        }
      }

      // 今天日期
      &.today {
        color: #4ea0fc !important;
        font-weight: bold !important;
      }

      // 今天且被选中的日期
      &.today.current:not(.disabled) {
        background-color: #4ea0fc !important;
        color: #ffffff !important;
        font-weight: bold !important;
        &.cell {
          color: #ffffff !important;
        }
      }

      // 禁用日期
      &.disabled {
        color: #6493d4 !important;
        background-color: transparent !important;
      }

      // 其他月份的日期
      &.prev-month,
      &.next-month {
        color: #6493d4 !important;
      }

      // 其他月份日期悬停
      &.prev-month:hover,
      &.next-month:hover {
        background-color: #254489 !important;
        color: #cce4ff !important;
      }
    }

    ::v-deep .el-picker-panel__icon-btn {
      color: #cce4ff !important;

      &:hover {
        color: #4ea0fc !important;
      }
    }

    ::v-deep .el-picker-panel__shortcut {
      color: #cce4ff !important;

      &:hover {
        color: #4ea0fc !important;
      }
    }

    // 日期范围选择器特殊样式
    ::v-deep .el-date-table td {
      // 范围选择中的日期
      &.in-range {
        background-color: rgba(78, 160, 252, 0.2) !important;
        color: #cce4ff !important;
      }

      // 范围开始日期
      &.start-date {
        background-color: #4ea0fc !important;
        color: #ffffff !important;
        font-weight: bold !important;
      }

      // 范围结束日期
      &.end-date {
        background-color: #4ea0fc !important;
        color: #ffffff !important;
        font-weight: bold !important;
      }

      // 范围选择悬停效果
      &.in-range:hover {
        background-color: rgba(78, 160, 252, 0.3) !important;
        color: #ffffff !important;
      }
    }

    // 时间选择器样式（如果有）
    ::v-deep .el-time-panel {
      background-color: #1a2e52 !important;
      border-color: #4f98f6 !important;
    }

    ::v-deep .el-time-panel__content {
      background-color: #1a2e52 !important;
    }

    ::v-deep .el-scrollbar__wrap {
      background-color: #1a2e52 !important;
    }

    ::v-deep .el-time-spinner__item {
      color: #cce4ff !important;

      &:hover {
        background-color: #254489 !important;
        color: #ffffff !important;
      }

      &.active {
        background-color: #4ea0fc !important;
        color: #ffffff !important;
        font-weight: bold !important;
      }
    }

    // 日期选择器头部样式
    ::v-deep .el-picker-panel__header {
      color: #cce4ff !important;
    }

    ::v-deep .el-picker-panel__header-label {
      color: #cce4ff !important;

      &:hover {
        color: #4ea0fc !important;
      }
    }

    ::v-deep .el-year-table td {
      color: #cce4ff !important;

      // 年份悬停状态
      &.available:hover {
        background-color: #254489 !important;
        color: #ffffff !important;
      }

      // 当前选中年份 - 确保高对比度
      &.current:not(.disabled) {
        background-color: #4ea0fc !important;
        color: #ffffff !important;
        font-weight: bold !important;
      }

      // 今年
      &.today {
        color: #4ea0fc !important;
        font-weight: bold !important;
      }

      // 今年且被选中
      &.today.current:not(.disabled) {
        background-color: #4ea0fc !important;
        color: #ffffff !important;
        font-weight: bold !important;
      }

      // 禁用年份
      &.disabled {
        color: #6493d4 !important;
        background-color: transparent !important;
      }
    }

    ::v-deep .el-month-table td {
      color: #cce4ff !important;

      // 月份悬停状态
      &.available:hover {
        background-color: #254489 !important;
        color: #ffffff !important;
      }

      // 当前选中月份 - 确保高对比度
      &.current:not(.disabled) {
        background-color: #4ea0fc !important;
        color: #ffffff !important;
        font-weight: bold !important;
      }

      // 当前月份
      &.today {
        color: #4ea0fc !important;
        font-weight: bold !important;
      }

      // 当前月份且被选中
      &.today.current:not(.disabled) {
        background-color: #4ea0fc !important;
        color: #ffffff !important;
        font-weight: bold !important;
      }

      // 禁用月份
      &.disabled {
        color: #6493d4 !important;
        background-color: transparent !important;
      }
    }

    // 按钮样式
    ::v-deep .el-button {
      &:not(.el-button--primary) {
        background-color: #1a2e52 !important;
        border-color: #4f98f6 !important;
        color: #cce4ff !important;

        &:hover {
          background-color: #254489 !important;
          border-color: #4ea0fc !important;
          color: #ffffff !important;
        }
      }
    }

    // 占位符颜色
    ::v-deep .el-input__inner::placeholder {
      color: #6493d4 !important;
    }

    // 清除按钮
    ::v-deep .el-input__suffix {
      .el-input__icon {
        color: #6493d4 !important;

        &:hover {
          color: #cce4ff !important;
        }
      }
    }

    // 月份范围选择器中间文字
    .month-range span {
      color: #cce4ff;
    }

    // 强制修复日期选择器可读性问题 - 使用更高优先级的选择器
    ::v-deep .el-picker-panel .el-month-table td.available.current {
      background-color: #4ea0fc !important;
      color: #ffffff !important;
      font-weight: bold !important;
    }

    ::v-deep .el-picker-panel .el-date-table td.available.current {
      background-color: #4ea0fc !important;
      color: #ffffff !important;
      font-weight: bold !important;
    }

    ::v-deep .el-picker-panel .el-year-table td.available.current {
      background-color: #4ea0fc !important;
      color: #ffffff !important;
      font-weight: bold !important;
    }

    // 修复选中状态的文字颜色 - 最高优先级
    ::v-deep .el-picker-panel .el-month-table td.current span,
    ::v-deep .el-picker-panel .el-date-table td.current span,
    ::v-deep .el-picker-panel .el-year-table td.current span {
      color: #ffffff !important;
      font-weight: bold !important;
    }

    // 修复悬停状态的文字颜色
    ::v-deep .el-picker-panel .el-month-table td:hover span,
    ::v-deep .el-picker-panel .el-date-table td:hover span,
    ::v-deep .el-picker-panel .el-year-table td:hover span {
      color: #ffffff !important;
    }

    // 确保所有日期选择器面板的背景色
    ::v-deep .el-picker-panel,
    ::v-deep .el-picker-panel__body,
    ::v-deep .el-picker-panel__content {
      background-color: #1a2e52 !important;
      border-color: #4f98f6 !important;
    }

    // 额外的强制修复 - 针对Element UI的具体类名
    ::v-deep .el-month-table td.current,
    ::v-deep .el-month-table td.current:hover,
    ::v-deep .el-date-table td.current,
    ::v-deep .el-date-table td.current:hover,
    ::v-deep .el-year-table td.current,
    ::v-deep .el-year-table td.current:hover {
      background-color: #4ea0fc !important;
      color: #ffffff !important;
      font-weight: bold !important;
    }

    // 针对月份表格的特殊修复 - 最高优先级
    ::v-deep .el-month-table {
      td {
        color: #cce4ff !important;

        &.current {
          background-color: #4ea0fc !important;
          color: #ffffff !important;
          font-weight: bold !important;

          // 强制修复内部文字颜色
          span,
          div,
          .cell {
            color: #ffffff !important;
            font-weight: bold !important;
          }
        }

        &:hover:not(.current) {
          background-color: #254489 !important;
          color: #ffffff !important;
        }

        &.today {
          color: #4ea0fc !important;
          font-weight: bold !important;
        }

        &.today.current {
          background-color: #4ea0fc !important;
          color: #ffffff !important;
          font-weight: bold !important;

          // 强制修复内部文字颜色
          span,
          div,
          .cell {
            color: #ffffff !important;
            font-weight: bold !important;
          }
        }
      }
    }

    // 超级强制修复 - 针对所有可能的月份选择器元素
    ::v-deep .el-picker-panel__body .el-month-table td.current,
    ::v-deep .el-picker-panel__body .el-month-table td.current:hover,
    ::v-deep .el-picker-panel__body .el-month-table td.current:focus,
    ::v-deep .el-picker-panel__body .el-month-table td.current:active {
      background-color: #4ea0fc !important;
      color: #ffffff !important;
      font-weight: bold !important;

      // 强制修复所有内部元素
      *,
      span,
      div,
      .cell,
      a {
        color: #ffffff !important;
        font-weight: bold !important;
      }
    }

    // 针对日期表格的特殊修复
    ::v-deep .el-date-table {
      td {
        color: #cce4ff !important;

        &.current {
          background-color: #4ea0fc !important;
          color: #ffffff !important;
          font-weight: bold !important;

          // 强制修复内部文字颜色
          span,
          div,
          .cell {
            color: #ffffff !important;
            font-weight: bold !important;
          }
        }

        &:hover:not(.current) {
          background-color: #254489 !important;
          color: #ffffff !important;
        }

        &.today {
          color: #4ea0fc !important;
          font-weight: bold !important;
        }

        &.today.current {
          background-color: #4ea0fc !important;
          color: #ffffff !important;
          font-weight: bold !important;
        }
      }
    }

    // 针对年份表格的特殊修复
    ::v-deep .el-year-table {
      td {
        color: #cce4ff !important;

        &.current {
          background-color: #4ea0fc !important;
          color: #ffffff !important;
          font-weight: bold !important;
        }

        &:hover:not(.current) {
          background-color: #254489 !important;
          color: #ffffff !important;
        }

        &.today {
          color: #4ea0fc !important;
          font-weight: bold !important;
        }

        &.today.current {
          background-color: #4ea0fc !important;
          color: #ffffff !important;
          font-weight: bold !important;
        }
      }
    }

    // 统一的日期选择器修复方案
    ::v-deep .el-picker-panel {
      --el-color-primary: #4ea0fc;
      --el-text-color-primary: #ffffff;
      --el-text-color-regular: #cce4ff;
    }

    // 统一修复所有表格的选中状态
    ::v-deep .el-month-table td.current,
    ::v-deep .el-date-table td.current,
    ::v-deep .el-year-table td.current {
      background-color: #4ea0fc !important;
      color: #ffffff !important;
      font-weight: bold !important;

      *,
      span,
      div,
      .cell,
      a {
        color: #ffffff !important;
        font-weight: bold !important;
      }
    }
  }
}

// 响应式布局优化
@media (max-width: 1200px) {
  .search-bar .form-body {
    gap: 12px;
  }

  ::v-deep .el-form-item {
    margin-bottom: 12px !important;
  }
}

// 确保select组件的下拉框不被遮挡
::v-deep .el-select-dropdown {
  z-index: 9999 !important;
}
</style>
