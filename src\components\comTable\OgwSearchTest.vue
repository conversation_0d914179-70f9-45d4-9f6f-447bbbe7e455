<template>
  <div class="test-container">
    <h2>OgwSearch 组件测试页面</h2>
    
    <div class="test-section">
      <h3>测试场景1：长文本选项</h3>
      <OgwSearch
        :fields="longTextFields"
        v-model="searchForm1"
        @search="handleSearch"
        @reset="handleReset"
      />
    </div>

    <div class="test-section">
      <h3>测试场景2：多个搜索项</h3>
      <OgwSearch
        :fields="multipleFields"
        v-model="searchForm2"
        @search="handleSearch"
        @reset="handleReset"
      />
    </div>

    <div class="test-section">
      <h3>测试场景3：混合类型</h3>
      <OgwSearch
        :fields="mixedFields"
        v-model="searchForm3"
        @search="handleSearch"
        @reset="handleReset"
      />
    </div>

    <div class="result-section">
      <h3>搜索结果</h3>
      <pre>{{ JSON.stringify(lastSearchResult, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import OgwSearch from './OgwSearch.vue';

export default {
  name: 'OgwSearchTest',
  components: {
    OgwSearch
  },
  data() {
    return {
      searchForm1: {},
      searchForm2: {},
      searchForm3: {},
      lastSearchResult: null,
      
      // 长文本选项测试
      longTextFields: [
        {
          label: "组织机构:",
          prop: "orgId",
          type: "select",
          options: [
            { label: "中国海洋石油集团有限公司深海石油开发分公司", value: "org1" },
            { label: "中国海洋石油集团有限公司海上石油勘探开发分公司", value: "org2" },
            { label: "中国海洋石油集团有限公司南海西部石油管理局", value: "org3" },
            { label: "短名称", value: "org4" }
          ]
        },
        {
          label: "平台名称:",
          prop: "platform",
          type: "select",
          options: [
            { label: "深海一号能源站超深水半潜式生产储油平台", value: "platform1" },
            { label: "海洋石油981深水半潜式钻井平台", value: "platform2" },
            { label: "普通平台", value: "platform3" }
          ]
        }
      ],
      
      // 多个搜索项测试
      multipleFields: [
        {
          label: "项目:",
          prop: "project",
          type: "select",
          options: [
            { label: "项目A", value: "projectA" },
            { label: "项目B", value: "projectB" }
          ]
        },
        {
          label: "状态:",
          prop: "status",
          type: "select",
          options: [
            { label: "进行中", value: "ongoing" },
            { label: "已完成", value: "completed" }
          ]
        },
        {
          label: "类型:",
          prop: "type",
          type: "select",
          options: [
            { label: "类型1", value: "type1" },
            { label: "类型2", value: "type2" }
          ]
        },
        {
          label: "优先级:",
          prop: "priority",
          type: "select",
          options: [
            { label: "高", value: "high" },
            { label: "中", value: "medium" },
            { label: "低", value: "low" }
          ]
        }
      ],
      
      // 混合类型测试
      mixedFields: [
        {
          label: "关键词:",
          prop: "keyword",
          type: "input",
          placeholder: "请输入关键词"
        },
        {
          label: "部门:",
          prop: "department",
          type: "select",
          options: [
            { label: "技术开发部门", value: "tech" },
            { label: "市场营销部门", value: "marketing" }
          ]
        },
        {
          label: "年份:",
          prop: "year",
          type: "year",
          placeholder: "选择年份"
        },
        {
          label: "月份:",
          prop: "month",
          type: "month",
          placeholder: "选择月份"
        },
        {
          prop: "monthRange",
          type: "monthrange",
          startPlaceholder: "开始月份",
          endPlaceholder: "结束月份"
        }
      ]
    };
  },
  methods: {
    handleSearch(formData) {
      this.lastSearchResult = formData;
      console.log('搜索参数:', formData);
    },
    handleReset(formData) {
      this.lastSearchResult = formData;
      console.log('重置参数:', formData);
    }
  }
};
</script>

<style lang="scss" scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
  
  h3 {
    margin-top: 0;
    color: #333;
    border-bottom: 2px solid #409eff;
    padding-bottom: 10px;
  }
}

.result-section {
  margin-top: 40px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 8px;
  
  h3 {
    margin-top: 0;
    color: #333;
  }
  
  pre {
    background-color: #fff;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #ddd;
    overflow-x: auto;
    font-size: 12px;
    line-height: 1.4;
  }
}

h2 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}
</style>
